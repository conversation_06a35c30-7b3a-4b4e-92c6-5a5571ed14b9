# LLM Ergebnisanalyse Dashboard

Ein Streamlit-Dashboard zur Analyse und Visualisierung der Leistung verschiedener Large Language Models (LLMs).

## Überblick

Dieses Dashboard ermöglicht die umfassende Analyse von LLM-Performance-Daten mit verschiedenen Metriken wie Erfolgsraten, Antwortzeiten und Fehlerverteilungen.

## Features

- **Erfolgsraten-Analyse** - Vergleich der Erfolgsraten zwischen verschiedenen LLMs
- **Geschwindigkeitsanalyse** - Messung der Antwortzeiten und Zeichen pro Sekunde
- **Fehleranalyse** - Verteilung und Kategorisierung von Fehlern
- **Kategorien-Vergleich** - Analyse nach Testkategorien (alle vs. wichtige Kategorien)
- **Detailansichten** - Drill-down von Kategorien → Testfälle → Varianten
- **Performance-Score** - Kombinierte Bewertung aus Erfolgsrate und Geschwindigkeit
- **Robustheitsanalyse** - Konsistenz der LLMs über verschiedene Testfälle

## Installation

1. Repository klonen:
```bash
git clone <repository-url>
cd Analyser
```

2. Abhängigkeiten installieren:
```bash
pip install -r requirements.txt
```

## Verwendung

Dashboard starten:
```bash
streamlit run main.py
```

Das Dashboard öffnet sich automatisch im Browser unter `http://localhost:8501`.

## Navigation

- **Sidebar**: Auswahl von Kategorien, Testfällen und Varianten
- **Filter**: LLM-Auswahl für fokussierte Analyse
- **Tabs**: Wechsel zwischen "Alle Kategorien" und "Wichtige Kategorien"

## Datenstruktur

Das Dashboard erwartet eine SQLite-Datenbank mit folgenden Spalten:
- `llm` - Name des LLM
- `category` - Testkategorie
- `test_case_id` - Testfall-ID
- `variant_number` - Varianten-Nummer
- `repetition_number` - Wiederholungs-Nummer
- `successful` - Erfolg (0/1)
- `error` - Fehlertyp
- `response_time` - Antwortzeit in Nanosekunden
- `character_count` - Anzahl Zeichen in der Antwort
- `response` - Vollständige Antwort

## Konfiguration

Einstellungen können in `config.py` angepasst werden:
- Datenbankpfad
- Erfolgsraten-Schwellenwerte (75%, 80%)
- Geschwindigkeits-Schwellenwert (100 Zeichen/Sekunde)

## Projektstruktur

```
├── main.py                     # Hauptanwendung
├── config.py                   # Konfiguration
├── utils/                      # Hilfsfunktionen
│   ├── data_functions.py       # Datenverarbeitung
│   ├── state_management.py     # Session State Management
│   └── color_mapping.py        # Farbzuordnungen
├── components/                 # UI-Komponenten
│   ├── global_filter.py        # Globale Filter
│   ├── navigation.py           # Navigation
│   └── visualizations/         # Visualisierungen
└── views/                      # Seitenansichten
    ├── home.py                 # Startseite
    ├── category.py             # Kategorienansicht
    ├── testcase.py             # Testfall-Ansicht
    └── variant.py              # Varianten-Ansicht
```

## Entwickelt mit

- **Streamlit** - Web-Framework
- **Plotly** - Interaktive Visualisierungen
- **Pandas** - Datenverarbeitung
- **SQLite** - Datenbank

## Lizenz

Dieses Projekt ist für Bildungszwecke entwickelt.
