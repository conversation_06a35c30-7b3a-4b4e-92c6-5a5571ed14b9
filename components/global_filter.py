"""Global filter components for the LLM analysis dashboard.

Provides filtering capabilities for LLMs and other criteria.
Filters are applied globally across all views and visualizations.
"""

import streamlit as st

from utils.data_functions import filter_by_llms, get_llms
from utils.state_management import get_state, set_state


def render_global_filter_sidebar(raw_data):
    """Render the global filter sidebar with all available filter options.

    Args:
        raw_data: The raw DataFrame to apply filters to
    """
    st.sidebar.title("Globale Filter")
    render_llm_selection_filter(raw_data)
    # Additional filters can be added here (e.g., success threshold)


def render_llm_selection_filter(raw_data):
    """Render LLM selection filter and update filtered data when selection changes.

    Allows users to select specific LLMs for analysis. If no LLMs are selected,
    all available LLMs are included in the analysis.

    Args:
        raw_data: The raw DataFrame to filter by selected LLMs
    """
    available_llms = get_llms(raw_data)

    st.sidebar.subheader("LLM-Auswahl")
    selected_llms = st.sidebar.multiselect(
        "LLM-Auswahl",
        placeholder='Alle LLMs werden gezeigt',
        options=available_llms,
        key="llm-select",
        label_visibility="collapsed"
    )

    # Default to all LLMs if none selected
    if not selected_llms:
        selected_llms = available_llms

    # Update session state only when selection changes to avoid unnecessary recalculations
    if get_state('selected_llms') != selected_llms:
        set_state('selected_llms', selected_llms)
        set_state('filtered_data', filter_by_llms(raw_data, selected_llms))


def render_success_threshold_filter():
    """Render success rate threshold filter (currently disabled).

    This filter would allow users to set a minimum success rate threshold
    for filtering results. Currently commented out in the main filter function.
    """
    st.sidebar.subheader("Erfolgsgrenze")
    success_threshold = st.sidebar.slider(
        "Erfolgsgrenze",
        min_value=0,
        max_value=100,
        value=75,
        step=1,
        format="%d%%",
        key="success-threshold-slider",
        label_visibility="collapsed"
    )

    # Update session state when threshold changes
    if get_state('success_threshold') != success_threshold:
        set_state('success_threshold', success_threshold)
