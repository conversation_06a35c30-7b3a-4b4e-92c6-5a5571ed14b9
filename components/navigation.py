"""Navigation components for hierarchical data exploration.

Provides a three-level navigation system: Category -> Testcase -> Variant.
Navigation state is maintained in Streamlit session state and automatically
resets child selections when parent selections change.
"""

from typing import Dict, Optional, Any

import streamlit as st

from utils.data_functions import get_categories, get_testcases_by_category, get_variant_numbers_by_testcase_id
from utils.state_management import get_state, set_state, get_or_initialize_state


def initialize_navigation_state() -> None:
    """Initialize navigation state variables in session state.

    Creates navigation state variables for category, testcase, and variant
    selections, all initially set to None (no selection).
    """
    get_or_initialize_state('nav_category', lambda: None)
    get_or_initialize_state('nav_testcase', lambda: None)
    get_or_initialize_state('nav_variant', lambda: None)


def get_current_navigation() -> Dict[str, Optional[Any]]:
    """Get the current navigation state as a dictionary.

    Returns:
        Dictionary containing current selections for category, testcase, and variant
    """
    return {
        'category': get_state('nav_category'),
        'testcase': get_state('nav_testcase'),
        'variant': get_state('nav_variant')
    }


def handle_category_selection_change():
    """Handle category selection change and reset child selections.

    When a new category is selected, automatically resets testcase and variant
    selections to None since they may no longer be valid.
    """
    set_state('nav_category', st.session_state.category_select)
    set_state('nav_testcase', None)
    set_state('nav_variant', None)


def handle_testcase_selection_change():
    """Handle testcase selection change and reset variant selection.

    When a new testcase is selected, automatically resets variant selection
    to None since it may no longer be valid for the new testcase.
    """
    set_state('nav_testcase', st.session_state.testcase_select)
    set_state('nav_variant', None)


def handle_variant_selection_change():
    """Handle variant selection change.

    Updates the navigation state when a variant is selected.
    No child selections to reset since variant is the deepest level.
    """
    set_state('nav_variant', st.session_state.variant_select)


def render_navigation_sidebar(df):
    """Render hierarchical navigation sidebar with category, testcase, and variant selection.

    Creates a three-level navigation system where each level depends on the previous:
    1. Category selection (always available)
    2. Testcase selection (available when category is selected)
    3. Variant selection (available when testcase is selected)

    Args:
        df: The DataFrame containing the navigation data
    """
    initialize_navigation_state()

    st.sidebar.title("Navigation")

    # Level 1: Category selection
    st.sidebar.selectbox(
        "Kategorie",
        options=get_categories(df),
        placeholder="Kategorie wählen...",
        key="category_select",
        on_change=handle_category_selection_change,
        index=None
    )

    # Level 2: Testcase selection (only if category is selected)
    current_category = get_state('nav_category')
    if current_category:
        available_testcases = get_testcases_by_category(df, current_category)
        st.sidebar.selectbox(
            "Testfall",
            options=[str(tc) for tc in available_testcases],
            placeholder="Testfall wählen...",
            key="testcase_select",
            on_change=handle_testcase_selection_change,
            index=None
        )

        # Level 3: Variant selection (only if testcase is selected)
        current_testcase = get_state('nav_testcase')
        if current_testcase:
            available_variants = get_variant_numbers_by_testcase_id(df, int(current_testcase))
            st.sidebar.selectbox(
                "Variante",
                options=[str(v) for v in available_variants],
                key="variant_select",
                placeholder="Variante wählen...",
                on_change=handle_variant_selection_change,
                index=None
            )
