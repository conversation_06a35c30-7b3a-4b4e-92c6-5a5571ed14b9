"""Accuracy vs Speed scatter plot visualization for LLM performance analysis.

Provides scatter plot visualization showing the relationship between LLM accuracy
(success rate) and speed (characters per second), with quadrant analysis and
performance threshold lines.
"""

import plotly.express as px
import streamlit as st

from utils.data_functions import get_llms
from utils.color_mapping import get_llm_color_mapping
from config import SUCCESS_RATE_THRESHOLD, HIGHER_SUCCESS_RATE_THRESHOLD, SPEED_THRESHOLD


@st.cache_data
def get_accuracy_vs_speed_chart(df, is_important_categories=False) -> px.scatter:
    """Create scatter plot visualization of LLM accuracy vs speed performance.

    Plots success rate (accuracy) against median response speed for each LLM,
    with threshold lines creating performance quadrants for easy analysis.

    Args:
        df: DataFrame containing LLM performance data
        is_important_categories: Whether this is the important categories view (affects thresholds)

    Returns:
        Plotly scatter plot figure with quadrant annotations and threshold lines
    """
    # Calculate aggregated performance metrics for each LLM
    llm_performance = df.groupby('llm').agg({
        'successful': 'mean',  # Success rate (0-1 scale)
        'characters_per_second': lambda x: x[df['successful'] == 1].median()  # Median speed for successful responses only
    }).reset_index()

    # LLMs will be ordered using get_unique_llms in category_orders

    # Convert and format data
    data['success_rate'] = data['successful'] * 100
    data['characters_per_second'] = data['characters_per_second'].round(2)

    # Calculate boundaries and centers for the plot
    x_min = data["characters_per_second"].min()
    x_max = data["characters_per_second"].max()
    y_min = 0  # Fixed at 0%
    y_max = 100  # Fixed at 100%
    margin_factor = 0.15  # Factor for margins

    # Calculate plot boundaries with consistent margins
    x_range = x_max - x_min
    y_range = y_max - y_min

    plot_x_min = x_min - (x_range * margin_factor)
    plot_x_max = x_max + (x_range * margin_factor)
    plot_y_min = y_min - (y_range * margin_factor)
    plot_y_max = y_max + (y_range * margin_factor)

    plot_width = plot_x_max - plot_x_min

    # Verwende die Grenzwerte aus der Konfiguration statt der Mittelwerte
    y_mid = SUCCESS_RATE_THRESHOLD  # Erfolgsraten-Grenzwert (70%)
    x_mid = SPEED_THRESHOLD  # Geschwindigkeits-Grenzwert (100 Zeichen/Sekunde)

    # Calculate quadrant centers
    x_left = (plot_x_min + x_mid) / 2  # Mitte zwischen linkem Rand und Grenzwert
    x_right = (x_mid + plot_x_max) / 2  # Mitte zwischen Grenzwert und rechtem Rand
    y_top = (y_mid + y_max) / 2  # Mitte zwischen Grenzwert und oberem Rand
    y_bottom = (y_min + y_mid) / 2  # Mitte zwischen unterem Rand und Grenzwert

    # Get LLMs in original order
    llms = get_llms(df)

    # Get color mapping for LLMs
    color_mapping = get_llm_color_mapping()

    # Create scatter plot
    fig = px.scatter(
        data,
        x="characters_per_second",
        y="success_rate",
        color="llm",
        color_discrete_map=color_mapping,
        size=[50] * len(data),  # Constant size for all points
        hover_name="llm",
        category_orders={'llm': llms},
        text="llm",
        title="LLM Leistungsmatrix",
        labels={
            "llm": "LLM",
            "characters_per_second": "Geschwindigkeit (Characters/Sekunde)",
            "success_rate": "Erfolgsrate",
        },
        height=500
    )

    # Add vertical line for speed threshold
    fig.add_shape(
        type="line",
        x0=x_mid,
        x1=x_mid,
        y0=plot_y_min,
        y1=plot_y_max,
        line=dict(color="gray", width=2, dash="dash"),
    )

    # Add horizontal line for success rate threshold (75%)
    fig.add_shape(
        type="line",
        x0=plot_x_min,
        x1=plot_x_max,
        y0=y_mid,
        y1=y_mid,
        line=dict(color="gray", width=2, dash="dash"),
    )

    # Add second horizontal line for higher success rate threshold (80%) if showing important categories
    if is_important_categories:
        fig.add_shape(
            type="line",
            x0=plot_x_min,
            x1=plot_x_max,
            y0=HIGHER_SUCCESS_RATE_THRESHOLD,
            y1=HIGHER_SUCCESS_RATE_THRESHOLD,
            line=dict(color="darkgray", width=2, dash="dash"),
        )

    # Add threshold labels
    fig.add_annotation(
        x=x_mid,
        y=plot_y_min,
        text=f"{SPEED_THRESHOLD} Zeichen/Sek.",
        showarrow=False,
        yanchor="top",
        font=dict(size=12, color="gray"),
    )

    fig.add_annotation(
        x=plot_x_min,
        y=y_mid,
        text=f"{SUCCESS_RATE_THRESHOLD}%",
        showarrow=False,
        xanchor="left",
        font=dict(size=12, color="gray"),
    )

    # Add label for higher success rate threshold if showing important categories
    if is_important_categories:
        fig.add_annotation(
            x=plot_x_min,
            y=HIGHER_SUCCESS_RATE_THRESHOLD,
            text=f"{HIGHER_SUCCESS_RATE_THRESHOLD}%",
            showarrow=False,
            xanchor="left",
            font=dict(size=12, color="darkgray"),
        )

    # Add quadrant labels with different colors at center of each quadrant
    fig.add_annotation(
        x=x_left,
        y=y_top,
        text="Langsam & Genau",
        showarrow=False,
        font=dict(size=12, color="orange"),
    )

    fig.add_annotation(
        x=x_right,
        y=y_top,
        text="Schnell & Genau",
        showarrow=False,
        font=dict(size=12, color="green"),
    )

    fig.add_annotation(
        x=x_left,
        y=y_bottom,
        text="Langsam & Ungenau",
        showarrow=False,
        font=dict(size=12, color="gray"),
    )

    fig.add_annotation(
        x=x_right,
        y=y_bottom,
        text="Schnell & Ungenau",
        showarrow=False,
        font=dict(size=12, color="gray"),
    )

    # Update layout
    fig.update_layout(
        showlegend=False,
        yaxis_range=[plot_y_min, plot_y_max],
        xaxis_range=[plot_x_min, plot_x_max],
        yaxis_ticksuffix="%",
    )

    # Update hover template and text position
    fig.update_traces(
        textposition="top center",  # Position the text above points
        hovertemplate="LLM: %{text}<br>Erfolgsrate: %{y:.1f}%<br>Characters/Sekunde: %{x:.1f}<extra></extra>",
    )

    return fig
