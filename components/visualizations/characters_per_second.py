"""Characters per second visualization for LLM speed analysis.

Provides box plot visualization of LLM response speed measured in
characters per second, focusing only on successful responses.
"""

import plotly.express as px
import streamlit as st

from utils.data_functions import get_llms
from utils.color_mapping import get_llm_color_mapping
from config import SPEED_THRESHOLD


@st.cache_data
def get_characters_per_second_chart(df) -> px.box:
    """Create box plot visualization of LLM response speed in characters per second.

    Analyzes only successful test results to provide accurate speed measurements.
    Includes speed threshold line for performance benchmarking.

    Args:
        df: DataFrame containing LLM performance data

    Returns:
        Plotly box chart figure showing speed distribution by LLM
    """
    # Filter to include only successful responses for accurate speed analysis
    successful_responses = df[df['successful'] == 1]

    # Get LLMs in original database order for consistent display
    llm_order = get_llms(df)

    # Get consistent color mapping for LLMs
    llm_colors = get_llm_color_mapping()

    # Create box plot showing speed distribution for each LLM
    speed_chart = px.box(
        successful_responses,
        x='llm',
        y='characters_per_second',
        title='Characters pro Sekunde nach LLM (nur erfolgreiche Tests)',
        color='llm',
        color_discrete_map=llm_colors,
        labels={'llm': 'LLM', 'characters_per_second': 'Characters pro Sekunde'},
        category_orders={'llm': llm_order}
    )

    # Clean up axis labels
    speed_chart.update_xaxes(title=None)

    # Hide legend since colors are already mapped to x-axis labels
    speed_chart.update_layout(showlegend=False)

    # Calculate number of LLMs for threshold line positioning
    num_llms = len(successful_responses['llm'].unique())

    # Add speed threshold reference line for performance benchmarking
    speed_chart.add_shape(
        type="line",
        x0=-0.5,  # Start left of the first LLM
        x1=num_llms - 0.5,  # End right of the last LLM
        y0=SPEED_THRESHOLD,
        y1=SPEED_THRESHOLD,
        line=dict(color="gray", width=2, dash="dash"),
    )

    # Add threshold annotation for reference
    speed_chart.add_annotation(
        x=num_llms - 0.5,  # Right-aligned
        y=SPEED_THRESHOLD,
        text=f"{SPEED_THRESHOLD} Zeichen/Sek.",
        showarrow=False,
        xanchor="right",
        yanchor="bottom",
        font=dict(size=12, color="gray"),
    )

    return speed_chart
