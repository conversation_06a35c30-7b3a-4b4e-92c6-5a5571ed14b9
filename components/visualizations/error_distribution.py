"""Error distribution visualization for LLM failure analysis.

Provides stacked bar chart visualization showing the distribution of different
error types across LLMs, with error category consolidation for better analysis.
"""

import plotly.express as px
import streamlit as st

from utils.data_functions import get_llms
from utils.color_mapping import get_llm_color_mapping


@st.cache_data
def get_error_distribution_chart(df) -> px.bar:
    """Create a stacked bar chart showing error distribution for each LLM.

    Args:
        df: DataFrame containing the data

    Returns:
        Plotly bar chart figure
    """
    # Create a copy of the dataframe to avoid modifying the original
    df_copy = df.copy()

    # Consolidate similar error types for cleaner visualization
    error_consolidation_mapping = {
        'TOO_MANY_BOOKINGS': 'WRONG_NUMBER_OF_BOOKINGS',
        'TOO_FEW_BOOKINGS': 'WRONG_NUMBER_OF_BOOKINGS',
        'NO_MATCHING_BOOKING': 'BOOKINGS_NOT_MATCHING',
        'DATETIME_NOT_MATCHING': 'BOOKINGS_NOT_MATCHING',
        'DATE_NOT_MATCHING': 'BOOKINGS_NOT_MATCHING',
        'TIME_NOT_MATCHING': 'BOOKINGS_NOT_MATCHING',
        'TIMEACCOUNT_NOT_MATCHING': 'BOOKINGS_NOT_MATCHING',
    }

    # Apply error consolidation mapping
    error_data['error'] = error_data['error'].replace(error_consolidation_mapping)

    # Group by LLM and error type, and count occurrences
    grouped = df_copy.groupby(['llm', 'error']).size().reset_index(name='count')

    # Get LLMs in original order
    llms = get_llms(df)

    # Get color mapping for LLMs
    color_mapping = get_llm_color_mapping()

    # Create a stacked bar chart
    fig = px.bar(grouped, x='llm', y='count', color='error', barmode='stack', title='Fehlerverteilung pro LLM',
                 labels={'llm': 'LLM', 'count': 'Anzahl', 'error': 'Fehler'}, category_orders={'llm': llms})
    fig.update_xaxes(title=None)

    return fig
