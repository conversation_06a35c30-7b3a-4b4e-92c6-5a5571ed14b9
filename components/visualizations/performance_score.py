"""Performance score visualization for comprehensive LLM evaluation.

Provides combined performance metrics that incorporate both success rate and speed,
offering a holistic view of LLM performance with weighted scoring.
"""

import plotly.express as px
import pandas as pd
import streamlit as st

from utils.data_functions import get_llms
from utils.color_mapping import get_llm_color_mapping

@st.cache_data
def get_performance_score_chart(df):
    """
    Creates a visualization showing a combined performance score for each LLM.

    The performance score combines accuracy (success rate) and speed (characters per second)
    into a single metric:

    Score = (Success Rate %) × (Characters per Second / Max Characters per Second) × 100

    Returns:
        plotly.graph_objects.Figure: A bar chart showing the performance scores.
    """
    # Calculate success rate for each LLM
    success_by_llm = df.groupby('llm')['successful'].mean().reset_index()
    success_by_llm.rename(columns={'successful': 'success_rate'}, inplace=True)

    # Calculate characters per second for each LLM
    speed_by_llm = df.copy()
    # Avoid division by zero
    speed_by_llm['response_time'] = speed_by_llm['response_time'].apply(lambda x: max(x, 0.001))
    # Calculate characters per second using character_count
    speed_by_llm['chars_per_second'] = speed_by_llm['character_count'] / speed_by_llm['response_time']
    speed_by_llm = speed_by_llm.groupby('llm')['chars_per_second'].mean().reset_index()

    # Merge the two dataframes
    performance_df = pd.merge(success_by_llm, speed_by_llm, on='llm')

    # Normalize characters per second (0-1 scale)
    max_chars_per_second = performance_df['chars_per_second'].max()
    performance_df['normalized_speed'] = (performance_df['chars_per_second'] / max_chars_per_second)

    # Calculate combined score (0-100 scale)
    performance_df['performance_score'] = performance_df['success_rate'] * performance_df['normalized_speed'] * 100

    # Get LLMs in original order
    llms = get_llms(df)

    # Get color mapping for LLMs
    color_mapping = get_llm_color_mapping()

    # Create bar chart
    fig = px.bar(
        performance_df,
        x='llm',
        y='performance_score',
        color='llm',
        color_discrete_map=color_mapping,
        title='Gesamtleistungswert (Erfolgsrate × Geschwindigkeit)',
        labels={'performance_score': 'Leistungswert', 'llm': 'LLM'},
        category_orders={'llm': llms}
    )

    # Update layout
    fig.update_xaxes(title=None)
    fig.update_yaxes(range=[0, 100])

    fig.update_layout(showlegend=False)

    return fig