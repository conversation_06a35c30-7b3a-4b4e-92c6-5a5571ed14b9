"""Response time estimation visualization for LLM performance prediction.

Provides visualizations for estimating response times based on expected output length,
using median speed calculations to predict performance for different booking scenarios.
"""

import plotly.express as px
import plotly.graph_objects as go
import pandas as pd
import streamlit as st
import numpy as np

from utils.data_functions import get_llms
from utils.color_mapping import get_llm_color_mapping
from config import SPEED_THRESHOLD


@st.cache_data
def get_response_time_estimation_chart(df):
    """
    Creates a visualization showing the estimated response times for different response lengths
    based on each model's median speed (characters per second).

    Args:
        df: DataFrame containing the data

    Returns:
        plotly.graph_objects.Figure: A chart showing the estimated response times
    """
    # Define the character counts for different response lengths
    char_counts = {
        "1 Buchung (210 Zeichen)": 210,
        "2 Buchungen (355 Zeichen)": 355,
        "5 Buchungen (790 Zeichen)": 790
    }

    # Calculate median characters per second for each LLM (only successful responses)
    successful_df = df[df['successful'] == 1]
    speed_by_llm = successful_df.groupby('llm')['characters_per_second'].median().reset_index()

    # Create a DataFrame for the visualization
    result_data = []

    for _, row in speed_by_llm.iterrows():
        llm_name = row['llm']
        median_speed = row['characters_per_second']

        for response_type, char_count in char_counts.items():
            # Calculate estimated response time in seconds
            estimated_time = char_count / median_speed

            result_data.append({
                'llm': llm_name,
                'response_type': response_type,
                'character_count': char_count,
                'median_speed': median_speed,
                'estimated_time': estimated_time
            })

    result_df = pd.DataFrame(result_data)

    # Sort by LLM order from the original dataframe
    llm_order = get_llms(df)
    result_df['llm_order'] = result_df['llm'].apply(lambda x: llm_order.index(x) if x in llm_order else 999)
    result_df = result_df.sort_values('llm_order')

    # Get color mapping for LLMs
    color_mapping = get_llm_color_mapping()

    # Create a stacked bar chart
    fig = px.bar(
        result_df,
        x='llm',
        y='estimated_time',
        color='response_type',
        color_discrete_map=color_mapping,
        barmode='stack',
        title='Geschätzte Antwortzeit nach Antwortlänge und LLM',
        labels={
            'estimated_time': 'Geschätzte Antwortzeit (Sekunden)',
            'llm': 'LLM',
            'response_type': 'Antwortlänge'
        },
        category_orders={
            'llm': llm_order,
            'response_type': list(char_counts.keys())
        },
        height=500
    )

    # Add median speed to the LLM labels
    # Create a new column for LLM labels with speed
    llm_speeds = {}
    for llm in result_df['llm'].unique():
        median_speed = result_df[result_df['llm'] == llm]['median_speed'].iloc[0]
        llm_speeds[llm] = f"{llm}<br>({median_speed:.1f} Z/s)"

    # Update the x-axis tick labels
    fig.update_layout(
        xaxis=dict(
            tickmode='array',
            tickvals=list(llm_speeds.keys()),
            ticktext=list(llm_speeds.values())
        )
    )

    # Update layout
    fig.update_layout(
        legend_title_text='Antwortlänge',
        xaxis_title=None,
        yaxis_title='Sekunden',
    )

    # Add reference line for SPEED_THRESHOLD
    # Calculate reference times for each character count
    reference_times = {char_count: char_count / SPEED_THRESHOLD for _, char_count in char_counts.items()}

    # Add annotations for reference times
    for response_type, char_count in char_counts.items():
        ref_time = reference_times[char_count]
        fig.add_shape(
            type="line",
            x0=-0.5,
            x1=len(llm_order) - 0.5,
            y0=ref_time,
            y1=ref_time,
            line=dict(color="gray", width=1, dash="dot"),
        )

        # Add label at the end of each line
        fig.add_annotation(
            x=len(llm_order) - 0.5,
            y=ref_time,
            text=f"{ref_time:.1f}s bei {SPEED_THRESHOLD} Z/s",
            showarrow=False,
            xanchor="right",
            yanchor="bottom",
            font=dict(size=10, color="gray"),
        )

    return fig


@st.cache_data
def get_response_time_line_chart(df):
    """
    Creates a line chart showing the estimated response times for different response lengths
    based on each model's median speed (characters per second).

    Args:
        df: DataFrame containing the data

    Returns:
        plotly.graph_objects.Figure: A line chart showing the estimated response times
    """
    # Define the character counts for different response lengths
    char_counts = {
        "1 Buchung (210 Zeichen)": 210,
        "2 Buchungen (355 Zeichen)": 355,
        "5 Buchungen (790 Zeichen)": 790
    }

    # Calculate median characters per second for each LLM (only successful responses)
    successful_df = df[df['successful'] == 1]
    speed_by_llm = successful_df.groupby('llm')['characters_per_second'].median().reset_index()

    # Create a DataFrame for the visualization
    result_data = []

    for _, row in speed_by_llm.iterrows():
        llm_name = row['llm']
        median_speed = row['characters_per_second']

        for response_type, char_count in char_counts.items():
            # Calculate estimated response time in seconds
            estimated_time = char_count / median_speed

            result_data.append({
                'llm': llm_name,
                'response_type': response_type,
                'character_count': char_count,
                'median_speed': median_speed,
                'estimated_time': estimated_time
            })

    result_df = pd.DataFrame(result_data)

    # Sort by LLM order from the original dataframe
    llm_order = get_llms(df)
    result_df['llm_order'] = result_df['llm'].apply(lambda x: llm_order.index(x) if x in llm_order else 999)
    result_df = result_df.sort_values('llm_order')

    # Get color mapping for LLMs
    color_mapping = get_llm_color_mapping()

    # Create a line chart
    fig = px.line(
        result_df,
        x='character_count',
        y='estimated_time',
        color='llm',
        color_discrete_map=color_mapping,
        markers=True,
        title='Geschätzte Antwortzeit nach Zeichenanzahl und LLM',
        labels={
            'estimated_time': 'Geschätzte Antwortzeit (Sekunden)',
            'character_count': 'Anzahl Zeichen',
            'llm': 'LLM'
        },
        category_orders={'llm': llm_order},
        height=500
    )

    # Add legend title with median speeds
    legend_title = "<b>LLM (Zeichen/Sekunde)</b><br>"
    for llm in result_df['llm'].unique():
        median_speed = result_df[result_df['llm'] == llm]['median_speed'].iloc[0]
        legend_title += f"{llm}: {median_speed:.1f} Z/s<br>"

    # Add vertical lines for the specific character counts
    for char_count in char_counts.values():
        fig.add_shape(
            type="line",
            x0=char_count,
            x1=char_count,
            y0=0,
            y1=result_df['estimated_time'].max() * 1.1,
            line=dict(color="gray", width=1, dash="dot"),
        )

    # Update layout
    fig.update_layout(
        xaxis_title='Anzahl Zeichen',
        yaxis_title='Sekunden',
        legend_title_text=legend_title
    )

    # Add reference line for SPEED_THRESHOLD
    # Calculate reference times for character counts starting from 200 to max
    max_chars = max(char_counts.values())
    x_values = list(range(190, 850, 50))
    y_values = [x / SPEED_THRESHOLD for x in x_values]

    # Add reference line
    fig.add_trace(
        go.Scatter(
            x=x_values,
            y=y_values,
            mode='lines',
            line=dict(color="gray", width=2, dash="dot"),
            name=f"{SPEED_THRESHOLD} Z/s Referenz",
            hovertemplate="Zeichen: %{x}<br>Zeit: %{y:.1f}s bei {SPEED_THRESHOLD} Z/s<extra></extra>"
        )
    )

    return fig
