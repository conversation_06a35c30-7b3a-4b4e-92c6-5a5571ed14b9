"""Success rate by category visualization for LLM performance analysis.

Provides bar chart visualizations showing success rates across different
test categories, with separate functions for all categories vs. important categories.
"""

import plotly.express as px
import streamlit as st

from utils.data_functions import get_categories, get_llms
from utils.color_mapping import get_llm_color_mapping
from config import SUCCESS_RATE_THRESHOLD, HIGHER_SUCCESS_RATE_THRESHOLD


@st.cache_data
def get_success_rate_for_categories(df):
    """Create grouped bar chart showing success rate per category for each LLM.

    Shows success rates broken down by both LLM and category with standard
    threshold line for performance benchmarking.

    Args:
        df: DataFrame containing LLM performance data

    Returns:
        Plotly grouped bar chart figure showing success rates by category and LLM
    """
    # Get categories in original order
    category_order = get_categories(df)

    # Group by LLM and category and calculate success rate
    grouped = df.groupby(['llm', 'category'])['successful'].apply(lambda x: x.mean() * 100).reset_index(
        name='success_rate')

    # Get LLMs in original order
    llms = get_llms(df)

    # Get color mapping for LLMs
    color_mapping = get_llm_color_mapping()

    # Create a bar chart with Plotly Express
    fig = px.bar(
        grouped,
        x='category',
        y='success_rate',
        color='llm',
        color_discrete_map=color_mapping,
        barmode='group',
        title='Erfolgsrate nach Kategorie und LLM',
        labels={'success_rate': 'Erfolgsrate', 'category': 'Kategorie', 'llm': 'LLM'},
        category_orders={'category': category_order, 'llm': llms}
    )
    fig.update_xaxes(title=None)
    fig.update_yaxes(range=[0, 100], ticksuffix='%')

    # Füge horizontale Linie für den Erfolgsraten-Grenzwert hinzu
    fig.add_shape(
        type="line",
        x0=-0.5,  # Beginnt links von der ersten Kategorie
        x1=len(category_order) - 0.5,  # Endet rechts von der letzten Kategorie
        y0=SUCCESS_RATE_THRESHOLD,
        y1=SUCCESS_RATE_THRESHOLD,
        line=dict(color="gray", width=2, dash="dash"),
    )

    # Füge Beschriftung für die Grenzlinie hinzu (nur die Zahl)
    fig.add_annotation(
        x=len(category_order) - 0.5,  # Rechts ausgerichtet
        y=SUCCESS_RATE_THRESHOLD,
        text=f"{SUCCESS_RATE_THRESHOLD}%",
        showarrow=False,
        xanchor="right",
        yanchor="bottom",
        font=dict(size=12, color="gray"),
    )

    return fig


@st.cache_data
def get_success_rate_for_important_categories(df, important_categories=None):
    """Create a bar chart showing success rate per category for each LLM, but only for important categories.
    Includes two threshold lines at 75% and 80%.

    Args:
        df: DataFrame containing the data
        important_categories: List of category names that are considered important.
                             If None, all categories are shown.

    Returns:
        Plotly bar chart figure
    """
    # Get categories in original order
    all_categories = get_categories(df)

    # Filter for important categories if specified
    if important_categories:
        category_order = [cat for cat in all_categories if cat in important_categories]
    else:
        category_order = all_categories

    # Group by LLM and category and calculate success rate
    grouped = df.groupby(['llm', 'category'])['successful'].apply(lambda x: x.mean() * 100).reset_index(
        name='success_rate')

    # Filter for important categories
    if important_categories:
        grouped = grouped[grouped['category'].isin(important_categories)]

    # Get LLMs in original order
    llms = get_llms(df)

    # Get color mapping for LLMs
    color_mapping = get_llm_color_mapping()

    # Create a bar chart with Plotly Express
    fig = px.bar(
        grouped,
        x='category',
        y='success_rate',
        color='llm',
        color_discrete_map=color_mapping,
        barmode='group',
        title='Erfolgsrate nach wichtiger Kategorie und LLM',
        labels={'success_rate': 'Erfolgsrate', 'category': 'Kategorie', 'llm': 'LLM'},
        category_orders={'category': category_order, 'llm': llms}
    )
    fig.update_xaxes(title=None)
    fig.update_yaxes(range=[0, 100], ticksuffix='%')

    # Füge horizontale Linie für den Erfolgsraten-Grenzwert (75%) hinzu
    fig.add_shape(
        type="line",
        x0=-0.5,  # Beginnt links von der ersten Kategorie
        x1=len(category_order) - 0.5,  # Endet rechts von der letzten Kategorie
        y0=SUCCESS_RATE_THRESHOLD,
        y1=SUCCESS_RATE_THRESHOLD,
        line=dict(color="gray", width=2, dash="dash"),
    )

    # Füge Beschriftung für die Grenzlinie (75%) hinzu
    fig.add_annotation(
        x=len(category_order) - 0.5,  # Rechts ausgerichtet
        y=SUCCESS_RATE_THRESHOLD,
        text=f"{SUCCESS_RATE_THRESHOLD}%",
        showarrow=False,
        xanchor="right",
        yanchor="bottom",
        font=dict(size=12, color="gray"),
    )

    # Füge zweite horizontale Linie für den höheren Erfolgsraten-Grenzwert (80%) hinzu
    fig.add_shape(
        type="line",
        x0=-0.5,  # Beginnt links von der ersten Kategorie
        x1=len(category_order) - 0.5,  # Endet rechts von der letzten Kategorie
        y0=HIGHER_SUCCESS_RATE_THRESHOLD,
        y1=HIGHER_SUCCESS_RATE_THRESHOLD,
        line=dict(color="darkgray", width=2, dash="dash"),
    )

    # Füge Beschriftung für die zweite Grenzlinie (80%) hinzu
    fig.add_annotation(
        x=len(category_order) - 0.5,  # Rechts ausgerichtet
        y=HIGHER_SUCCESS_RATE_THRESHOLD,
        text=f"{HIGHER_SUCCESS_RATE_THRESHOLD}%",
        showarrow=False,
        xanchor="right",
        yanchor="bottom",
        font=dict(size=12, color="darkgray"),
    )

    return fig
