"""Success rate visualization for LLM performance comparison.

Provides bar chart visualization showing success rates across different LLMs
with configurable threshold lines for performance benchmarking.
"""

import plotly.express as px
import streamlit as st

from utils.data_functions import get_llms
from utils.color_mapping import get_llm_color_mapping
from config import SUCCESS_RATE_THRESHOLD, HIGHER_SUCCESS_RATE_THRESHOLD


@st.cache_data
def get_success_rate_for_llms_chart(df, is_important_categories=False) -> px.bar:
    """Create bar chart visualization of success rates across LLMs.

    Calculates and displays success rate percentages for each LLM with
    threshold lines for performance benchmarking.

    Args:
        df: DataFrame containing LLM performance data
        is_important_categories: Whether this is the important categories view (affects thresholds)

    Returns:
        Plotly bar chart figure showing success rates by LLM
    """
    # Calculate success rate for each LLM
    llm_success_rates = df.groupby('llm')['successful'].apply(lambda x: x.mean() * 100).reset_index(name='success_rate')

    # Get LLMs in original database order for consistent display
    llm_order = get_llms(df)

    # Get consistent color mapping for LLMs
    llm_colors = get_llm_color_mapping()

    # Create bar chart showing success rates
    success_rate_chart = px.bar(
        llm_success_rates,
        x='llm',
        y='success_rate',
        title='Erfolgsrate nach LLM',
        color='llm',
        color_discrete_map=llm_colors,
        labels={'success_rate': 'Erfolgsrate', 'llm': 'LLM'},
        category_orders={'llm': llm_order}
    )

    # Configure chart appearance
    success_rate_chart.update_xaxes(title=None)
    success_rate_chart.update_yaxes(range=[0, 100], ticksuffix='%')
    success_rate_chart.update_layout(showlegend=False)

    # Füge horizontale Linie für den Erfolgsraten-Grenzwert (75%) hinzu
    fig.add_shape(
        type="line",
        x0=-0.5,  # Beginnt links von der ersten Säule
        x1=len(grouped) - 0.5,  # Endet rechts von der letzten Säule
        y0=SUCCESS_RATE_THRESHOLD,
        y1=SUCCESS_RATE_THRESHOLD,
        line=dict(color="gray", width=2, dash="dash"),
    )

    # Füge Beschriftung für die Grenzlinie (75%) hinzu
    fig.add_annotation(
        x=len(grouped) - 0.5,  # Rechts ausgerichtet
        y=SUCCESS_RATE_THRESHOLD,
        text=f"{SUCCESS_RATE_THRESHOLD}%",
        showarrow=False,
        xanchor="right",
        yanchor="bottom",
        font=dict(size=12, color="gray"),
    )

    # Füge zweite horizontale Linie für den höheren Erfolgsraten-Grenzwert (80%) hinzu, wenn wichtige Kategorien angezeigt werden
    if is_important_categories:
        fig.add_shape(
            type="line",
            x0=-0.5,  # Beginnt links von der ersten Säule
            x1=len(grouped) - 0.5,  # Endet rechts von der letzten Säule
            y0=HIGHER_SUCCESS_RATE_THRESHOLD,
            y1=HIGHER_SUCCESS_RATE_THRESHOLD,
            line=dict(color="darkgray", width=2, dash="dash"),
        )

        # Füge Beschriftung für die zweite Grenzlinie (80%) hinzu
        fig.add_annotation(
            x=len(grouped) - 0.5,  # Rechts ausgerichtet
            y=HIGHER_SUCCESS_RATE_THRESHOLD,
            text=f"{HIGHER_SUCCESS_RATE_THRESHOLD}%",
            showarrow=False,
            xanchor="right",
            yanchor="bottom",
            font=dict(size=12, color="darkgray"),
        )

    return fig
