"""Success rate by testcase visualization for detailed LLM analysis.

Provides grouped bar chart visualization showing success rates across different
testcases, broken down by LLM for detailed performance comparison.
"""

import plotly.express as px
import streamlit as st

from utils.data_functions import get_llms
from utils.color_mapping import get_llm_color_mapping
from config import SUCCESS_RATE_THRESHOLD


@st.cache_data
def get_success_rate_for_testcases_chart(df):
    """Create a bar chart showing success rate for testcases for each LLM.

    Args:
        df: DataFrame containing the data

    Returns:
        Plotly bar chart figure
    """
    # Group by LLM and testcase ID and calculate success rate
    grouped = df.groupby(['llm', 'test_case_id'])['successful'].apply(lambda x: x.mean() * 100).reset_index(
        name='success_rate')

    # Get LLMs in original order
    llms = get_llms(df)

    # Get color mapping for LLMs
    color_mapping = get_llm_color_mapping()

    # Create a bar chart with Plotly Express
    fig = px.bar(
        grouped,
        x='test_case_id',
        y='success_rate',
        color='llm',
        color_discrete_map=color_mapping,
        barmode='group',
        title='Erfolgsrate pro Testfall und LLM',
        labels={'success_rate': 'Erfolgsrate', 'test_case_id': 'Testfall ID', 'llm': 'LLM'},
        category_orders={'llm': llms}
    )
    fig.update_yaxes(range=[0, 100], ticksuffix='%')

    # Testfall-IDs ermitteln und sortieren
    test_case_ids = sorted(df['test_case_id'].unique())

    # Füge horizontale Linie für den Erfolgsraten-Grenzwert hinzu
    # Verwende die tatsächlichen Testfall-IDs für die X-Achse
    fig.add_shape(
        type="line",
        x0=min(test_case_ids) - 0.5,  # Beginnt links vom ersten Testfall
        x1=max(test_case_ids) + 0.5,  # Endet rechts vom letzten Testfall
        y0=SUCCESS_RATE_THRESHOLD,
        y1=SUCCESS_RATE_THRESHOLD,
        line=dict(color="gray", width=2, dash="dash"),
    )

    # Füge Beschriftung für die Grenzlinie hinzu (nur die Zahl)
    fig.add_annotation(
        x=max(test_case_ids) + 0.5,  # Rechts ausgerichtet
        y=SUCCESS_RATE_THRESHOLD,
        text=f"{SUCCESS_RATE_THRESHOLD}%",
        showarrow=False,
        xanchor="right",
        yanchor="bottom",
        font=dict(size=12, color="gray"),
    )

    return fig
