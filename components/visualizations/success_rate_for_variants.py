"""Success rate by variant visualization for detailed LLM analysis.

Provides grouped bar chart visualization showing success rates across different
variants within a testcase, broken down by LLM for detailed performance comparison.
"""

import plotly.express as px
import streamlit as st

from utils.data_functions import get_llms
from utils.color_mapping import get_llm_color_mapping
from config import SUCCESS_RATE_THRESHOLD


@st.cache_data
def get_success_rate_for_variants_chart(df):
    """Create a bar chart showing success rate for variants for each LLM.

    Args:
        df: DataFrame containing the data

    Returns:
        Plotly bar chart figure
    """
    # Group by LLM, testcase ID, and variant number and calculate success rate
    grouped = df.groupby(['llm', 'test_case_id', 'variant_number'])['successful'].apply(
        lambda x: x.mean() * 100).reset_index(name='success_rate')

    # Get LLMs in original order
    llms = get_llms(df)

    # Get color mapping for LLMs
    color_mapping = get_llm_color_mapping()

    # Create a bar chart with Plotly Express
    fig = px.bar(
        grouped,
        x='variant_number',
        y='success_rate',
        color='llm',
        color_discrete_map=color_mapping,
        barmode='group',
        title='Erfolgsrate pro Variante und LLM',
        labels={'success_rate': 'Erfolgsrate', 'variant_number': 'Nummer der Variante', 'llm': 'LLM'},
        category_orders={'llm': llms}
    )
    fig.update_yaxes(range=[0, 100], ticksuffix='%')

    # Varianten-Nummern ermitteln und sortieren
    variant_numbers = sorted(df['variant_number'].unique())

    # Füge horizontale Linie für den Erfolgsraten-Grenzwert hinzu
    # Verwende die tatsächlichen Varianten-Nummern für die X-Achse
    fig.add_shape(
        type="line",
        x0=min(variant_numbers) - 0.5,  # Beginnt links von der ersten Variante
        x1=max(variant_numbers) + 0.5,  # Endet rechts von der letzten Variante
        y0=SUCCESS_RATE_THRESHOLD,
        y1=SUCCESS_RATE_THRESHOLD,
        line=dict(color="gray", width=2, dash="dash"),
    )

    # Füge Beschriftung für die Grenzlinie hinzu (nur die Zahl)
    fig.add_annotation(
        x=max(variant_numbers) + 0.5,  # Rechts ausgerichtet
        y=SUCCESS_RATE_THRESHOLD,
        text=f"{SUCCESS_RATE_THRESHOLD}%",
        showarrow=False,
        xanchor="right",
        yanchor="bottom",
        font=dict(size=12, color="gray"),
    )

    return fig
