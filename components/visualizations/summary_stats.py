"""Summary statistics calculation for LLM performance data.

Provides key metrics and statistics for analyzing LLM performance
across different dimensions like response time, success rates, and throughput.
"""

import pandas as pd
import streamlit as st


@st.cache_data
def get_summary_stats(df: pd.DataFrame) -> dict:
    """Calculate comprehensive summary statistics for LLM performance data.

    Computes key metrics including record counts, unique entities,
    and average performance indicators.

    Args:
        df: The DataFrame containing LLM performance data to analyze

    Returns:
        Dictionary containing summary statistics with keys:
        - total_records: Total number of test records
        - unique_llms: Number of unique LLMs tested
        - unique_categories: Number of unique test categories
        - unique_testcases: Number of unique test cases
        - avg_response_time: Average response time in seconds
        - avg_characters_per_second: Average throughput in characters per second
    """
    summary_statistics = {
        'total_records': len(df),
        'unique_llms': len(df['llm'].unique()),
        'unique_categories': len(df['category'].unique()),
        'unique_testcases': len(df['test_case_id'].unique()),
        'avg_response_time': df['response_time'].mean(),
        'avg_characters_per_second': df['characters_per_second'].mean(),
    }
    return summary_statistics
