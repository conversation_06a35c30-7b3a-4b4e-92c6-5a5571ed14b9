"""Variant robustness visualization for LLM consistency analysis.

Provides violin plot visualization showing the distribution of success rates
across different test cases, helping assess LLM consistency and robustness.
"""

import plotly.express as px
import streamlit as st
import pandas as pd

from utils.data_functions import get_llms
from utils.color_mapping import get_llm_color_mapping
from config import SUCCESS_RATE_THRESHOLD, HIGHER_SUCCESS_RATE_THRESHOLD

@st.cache_data
def get_variant_robustness_chart(df, is_important_categories=False):
    """
    Creates a visualization showing the distribution of success rates across test cases for each LLM.

    For each test case and LLM, calculates the success rate across all variants and repetitions,
    showing how consistently each model performs across different test cases.

    Args:
        df: DataFrame containing the data
        is_important_categories: Whether this is the important categories view

    Returns:
        tuple: (plotly.graph_objects.Figure, pandas.DataFrame) A violin plot showing the distribution
               of success rates and a DataFrame with statistics for display in a table.
    """
    # Calculate success rate for each test case and LLM
    test_case_success = df.groupby(['test_case_id', 'llm'])['successful'].mean().reset_index()

    # Convert to percentage
    test_case_success['success_rate'] = test_case_success['successful'] * 100

    # Get LLMs in original order
    llms = get_llms(df)

    # Get color mapping for LLMs
    color_mapping = get_llm_color_mapping()

    # Create violin plot
    fig = px.violin(
        test_case_success,
        x='llm',
        y='success_rate',
        color='llm',
        color_discrete_map=color_mapping,
        title='Verteilung der Erfolgsraten über verschiedene Testfälle',
        labels={'llm': 'LLM', 'success_rate': 'Erfolgsrate pro Testfall (%)'},
        category_orders={'llm': llms},
    )

    # Update layout
    fig.update_xaxes(title=None)
    fig.update_layout(showlegend=False)

    # Anzahl der LLMs ermitteln
    num_llms = len(df['llm'].unique())

    # Füge horizontale Linie für den Erfolgsraten-Grenzwert (75%) hinzu
    fig.add_shape(
        type="line",
        x0=-0.5,  # Beginnt links vom ersten LLM
        x1=num_llms - 0.5,  # Endet rechts vom letzten LLM
        y0=SUCCESS_RATE_THRESHOLD,
        y1=SUCCESS_RATE_THRESHOLD,
        line=dict(color="gray", width=2, dash="dash"),
    )

    # Füge Beschriftung für die Grenzlinie (75%) hinzu
    fig.add_annotation(
        x=num_llms - 0.5,  # Rechts ausgerichtet
        y=SUCCESS_RATE_THRESHOLD,
        text=f"{SUCCESS_RATE_THRESHOLD}%",
        showarrow=False,
        xanchor="right",
        yanchor="bottom",
        font=dict(size=12, color="gray"),
    )

    # Füge zweite horizontale Linie für den höheren Erfolgsraten-Grenzwert (80%) hinzu, wenn wichtige Kategorien angezeigt werden
    if is_important_categories:
        fig.add_shape(
            type="line",
            x0=-0.5,  # Beginnt links vom ersten LLM
            x1=num_llms - 0.5,  # Endet rechts vom letzten LLM
            y0=HIGHER_SUCCESS_RATE_THRESHOLD,
            y1=HIGHER_SUCCESS_RATE_THRESHOLD,
            line=dict(color="darkgray", width=2, dash="dash"),
        )

        # Füge Beschriftung für die zweite Grenzlinie (80%) hinzu
        fig.add_annotation(
            x=num_llms - 0.5,  # Rechts ausgerichtet
            y=HIGHER_SUCCESS_RATE_THRESHOLD,
            text=f"{HIGHER_SUCCESS_RATE_THRESHOLD}%",
            showarrow=False,
            xanchor="right",
            yanchor="bottom",
            font=dict(size=12, color="darkgray"),
        )

    # Berechne Statistiken für jedes LLM: Prozentsatz der Testfälle mit 100%, 0% und dazwischen
    stats = []
    for llm in test_case_success['llm'].unique():
        llm_data = test_case_success[test_case_success['llm'] == llm]
        total_cases = len(llm_data)
        perfect_cases = len(llm_data[llm_data['success_rate'] == 100])
        failed_cases = len(llm_data[llm_data['success_rate'] == 0])
        partial_cases = total_cases - perfect_cases - failed_cases

        stats.append({
            'LLM': llm,
            'Testfälle mit 100% Erfolg': f"{perfect_cases} ({round(perfect_cases / total_cases * 100, 1)}%)",
            'Testfälle mit 0% Erfolg': f"{failed_cases} ({round(failed_cases / total_cases * 100, 1)}%)",
            'Testfälle mit teilweisem Erfolg': f"{partial_cases} ({round(partial_cases / total_cases * 100, 1)}%)",
            'Gesamtanzahl Testfälle': total_cases
        })

    stats_df = pd.DataFrame(stats)

    return fig, stats_df
