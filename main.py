"""Main application entry point for LLM Performance Analysis Dashboard.

This Streamlit application provides comprehensive analysis and visualization
of Large Language Model (LLM) performance data including success rates,
response times, and error distributions.
"""

import streamlit as st

from components.global_filter import render_global_filter_sidebar
from components.navigation import render_navigation_sidebar, get_current_navigation
from config import DB_PATH, TABLE_NAME
from utils.data_functions import load_data, get_llms
from utils.color_mapping import initialize_color_mapping
from utils.state_management import get_or_initialize_state
from views.category import category
from views.home import home
from views.testcase import testcase
from views.variant import variant

# Configure Streamlit page settings
st.set_page_config(
    page_title="LLM Ergebnisanalyse",
    page_icon="📊",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Load and cache raw data from database
raw_data = get_or_initialize_state('raw_data', lambda: load_data(DB_PATH, TABLE_NAME))

# Initialize consistent color mapping for all LLMs across visualizations
if 'color_mapping_initialized' not in st.session_state:
    all_llms = get_llms(raw_data)
    initialize_color_mapping(all_llms)
    st.session_state['color_mapping_initialized'] = True

# Render sidebar components
render_navigation_sidebar(raw_data)
st.sidebar.divider()
render_global_filter_sidebar(raw_data)


def render_current_page():
    """Route to the appropriate page based on current navigation state.

    Navigation hierarchy:
    1. Variant view - if both testcase and variant are selected
    2. Testcase view - if only testcase is selected
    3. Category view - if only category is selected
    4. Home view - default overview page
    """
    navigation_state = get_current_navigation()

    # Route based on navigation depth (most specific first)
    if navigation_state['variant'] is not None and navigation_state['testcase'] is not None:
        variant(navigation_state['testcase'], navigation_state['variant'])
    elif navigation_state['testcase'] is not None:
        testcase(navigation_state['testcase'])
    elif navigation_state['category'] is not None:
        category(navigation_state['category'])
    else:
        home()


# Render the current page based on navigation state
render_current_page()
