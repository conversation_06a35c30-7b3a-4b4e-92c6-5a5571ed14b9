import streamlit as st
import pandas as pd
import numpy as np
import sqlite3

from components.visualizations.response_time_estimation import get_response_time_estimation_chart, get_response_time_line_chart
from config import DB_PATH, TABLE_NAME

# Set page configuration
st.set_page_config(
    page_title="Antwortzeit-Schätzung",
    page_icon="⏱️",
    layout="wide"
)

@st.cache_data
def load_test_data():
    """Load data for response time testing.

    Attempts to load from database first, falls back to sample data if unavailable.

    Returns:
        DataFrame with processed response time data
    """
    try:
        # Attempt to load real data from database
        conn = sqlite3.connect(DB_PATH)
        query = f"SELECT * FROM {TABLE_NAME}"
        df = pd.read_sql_query(query, conn)
        conn.close()

        # Process response time data (convert nanoseconds to seconds)
        df['response_time'] = (df['response_time'] / 1_000_000_000)

        # Calculate performance metrics
        df['characters_per_second'] = df['character_count'] / df['response_time']

        return df
    except Exception as e:
        # Generate sample data if database is unavailable
        st.warning(f"Konnte Datenbank nicht laden: {e}. Verwende Beispieldaten.")
        return generate_sample_data()


def generate_sample_data():
    """Generate sample data for testing response time visualizations.

    Returns:
        DataFrame with synthetic LLM performance data
    """
    # Define sample LLMs for testing
    sample_llms = ["GPT-4", "Claude 3", "Gemini", "Llama 3", "Mistral"]

        # Sample data with realistic speeds
        data = []
        for llm in llms:
            # Assign different speed ranges to different models
            if llm == "GPT-4":
                base_speed = np.random.uniform(80, 120)
            elif llm == "Claude 3":
                base_speed = np.random.uniform(100, 150)
            elif llm == "Gemini":
                base_speed = np.random.uniform(70, 110)
            elif llm == "Llama 3":
                base_speed = np.random.uniform(90, 130)
            else:  # Mistral
                base_speed = np.random.uniform(60, 100)

            # Create 20 samples for each LLM with some variation
            for _ in range(20):
                speed = base_speed * np.random.uniform(0.8, 1.2)  # Add some variation
                char_count = np.random.randint(100, 1000)
                response_time = char_count / speed

                data.append({
                    'llm': llm,
                    'character_count': char_count,
                    'response_time': response_time,
                    'characters_per_second': speed,
                    'successful': 1  # All successful for this demo
                })

        return pd.DataFrame(data)

# Load the data
data = load_data()

# Display the charts
st.title("Geschätzte Antwortzeit nach Antwortlänge")

# Create tabs for the two chart types
tab1, tab2 = st.tabs(["Säulendiagramm", "Liniendiagramm"])

with tab1:
    st.plotly_chart(get_response_time_estimation_chart(data), use_container_width=True)

with tab2:
    st.plotly_chart(get_response_time_line_chart(data), use_container_width=True)

# Show the data used for the charts
with st.expander("Daten anzeigen"):
    # Calculate and display median speeds
    median_speeds = data.groupby('llm')['characters_per_second'].median().reset_index()
    median_speeds.columns = ['LLM', 'Median Geschwindigkeit (Zeichen/Sekunde)']
    st.dataframe(median_speeds, use_container_width=True)
