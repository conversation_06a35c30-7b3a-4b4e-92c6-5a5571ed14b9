"""Data loading and processing utilities for LLM performance analysis.

This module provides functions for loading data from SQLite database,
extracting unique values, and filtering data based on various criteria.
All functions maintain SQLite column names unchanged.
"""

import sqlite3
from typing import List, Optional

import pandas as pd
import streamlit as st

# Constants for data processing
NANOSECONDS_TO_SECONDS = 1_000_000_000


@st.cache_data
def load_data(db_path: str, table_name: str = 'test_results') -> pd.DataFrame:
    """Load and process LLM performance data from SQLite database.

    Loads raw data and calculates derived metrics like characters per second.
    Response times are converted from nanoseconds to seconds for easier analysis.

    Args:
        db_path: Path to the SQLite database file
        table_name: Name of the table containing test results

    Returns:
        Processed DataFrame with calculated metrics ready for analysis
    """
    # Connect to database and execute query
    conn = sqlite3.connect(db_path)
    query = f"SELECT * FROM {table_name}"
    df = pd.read_sql_query(query, conn)
    conn.close()

    # Process response time data
    df['response_time'] = df['response_time'] / NANOSECONDS_TO_SECONDS

    # Calculate performance metrics
    df['characters_per_second'] = df['character_count'] / df['response_time']

    return df


# Data extraction functions
@st.cache_data
def get_llms(df: pd.DataFrame) -> List[str]:
    """
    Get all unique LLM names in the order they appear in the database.

    Args:
        df: The DataFrame to extract LLM names from

    Returns:
        List of unique LLM names in original database order
    """
    return df['llm'].unique().tolist()


@st.cache_data
def get_categories(df: pd.DataFrame) -> List[str]:
    """
    Get all unique categories in the order they appear in the database.

    Args:
        df: The DataFrame to extract categories from

    Returns:
        List of unique categories in original order
    """
    return df['category'].drop_duplicates().tolist()


@st.cache_data
def get_testcases_by_category(df: pd.DataFrame, category: str) -> List[int]:
    """
    Get all testcase IDs for a specific category.

    Args:
        df: The DataFrame to extract from
        category: Category name to filter by

    Returns:
        List of unique testcase IDs for the specified category
    """
    return df[df['category'] == category]['test_case_id'].unique().tolist()


@st.cache_data
def get_variant_numbers_by_testcase_id(df: pd.DataFrame, testcase_id: int) -> List[int]:
    """
    Get all variant numbers for a specific testcase.

    Args:
        df: The DataFrame to extract from
        testcase_id: Testcase ID to filter by

    Returns:
        List of unique variant numbers for the specified testcase
    """
    return df[df['test_case_id'] == int(testcase_id)]['variant_number'].unique().tolist()


# Data filtering functions
@st.cache_data
def filter_by_llms(df: pd.DataFrame, llm_names: Optional[List[str]] = None) -> pd.DataFrame:
    """
    Filter DataFrame to include only specified LLMs.

    Args:
        df: The DataFrame to filter
        llm_names: List of LLM names to include. If None or empty, returns original DataFrame

    Returns:
        Filtered DataFrame containing only rows for specified LLMs
    """
    if not llm_names:
        return df
    return df[df['llm'].isin(llm_names)]
