"""Category view for detailed analysis of a specific category.

Provides focused analysis of LLM performance within a selected category,
including testcase-level breakdowns and category-specific visualizations.
"""

import streamlit as st

from components.visualizations.accuracy_speed import get_accuracy_vs_speed_chart
from components.visualizations.success_rate_for_testcases import get_success_rate_for_testcases_chart
from components.visualizations.summary_stats import get_summary_stats
from utils.data_functions import get_testcases_by_category
from utils.state_management import get_state


def category(category_name):
    """Render detailed analysis page for a specific category.

    Displays category-specific performance metrics, testcase breakdowns,
    and comparative visualizations for the selected category.

    Args:
        category_name: The name of the category to analyze
    """
    # Get filtered data from session state
    raw_data = get_state('filtered_data')

    # Get all testcases belonging to the selected category
    category_testcases = get_testcases_by_category(raw_data, category_name)

    # Filter data to include only testcases from this category
    category_data = raw_data[raw_data['test_case_id'].isin(category_testcases)]

    st.title(f"Kategorie: {category_name}")

    # Display category-specific summary statistics
    display_category_statistics(category_data)

    # Display category-specific visualizations
    display_category_charts(category_data)


def display_category_statistics(category_data):
    """Display summary statistics for the category.

    Args:
        category_data: DataFrame containing data filtered for the specific category
    """
    stats = get_summary_stats(category_data)

    col1, col2, col3, col4 = st.columns(4)
    with col1:
        st.metric("Anzahl Testfälle", stats['unique_testcases'])
    with col2:
        st.metric("Gesamtanzahl Datensätze", stats['total_records'])
    with col3:
        st.metric("Durchschn. Antwortzeit (s)", round(stats['avg_response_time'], 2))
    with col4:
        st.metric("Durchschn. Characters/Sekunde", round(stats['avg_characters_per_second'], 2))


def display_category_charts(category_data):
    """Display category-specific visualization charts.

    Args:
        category_data: DataFrame containing data filtered for the specific category
    """
    # Success rate breakdown by individual testcases
    st.plotly_chart(get_success_rate_for_testcases_chart(category_data), use_container_width=True)

    # Accuracy vs. Speed analysis for this category
    st.plotly_chart(get_accuracy_vs_speed_chart(category_data), use_container_width=True)
