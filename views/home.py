"""Home page view for LLM performance analysis dashboard.

Provides comprehensive overview of all LLM performance data with summary statistics
and various visualizations. Includes tabs for all categories vs. important categories.
"""

import streamlit as st

from components.visualizations.accuracy_speed import get_accuracy_vs_speed_chart
from components.visualizations.error_distribution import get_error_distribution_chart
from components.visualizations.success_rate_for_categories import get_success_rate_for_categories, get_success_rate_for_important_categories
from components.visualizations.success_rate_for_llms import get_success_rate_for_llms_chart
from components.visualizations.summary_stats import get_summary_stats
from components.visualizations.characters_per_second import get_characters_per_second_chart
from components.visualizations.variant_robustness import get_variant_robustness_chart
from components.visualizations.performance_score import get_performance_score_chart
from components.visualizations.response_time_estimation import get_response_time_estimation_chart, get_response_time_line_chart
from utils.state_management import get_state


def home():
    """Render the home page with comprehensive LLM performance overview.

    Displays summary statistics and various charts organized in tabs for
    all categories vs. important categories analysis.
    """
    st.title("Ergebnisanalyse")

    # Get filtered data from session state
    filtered_data = get_state('filtered_data')

    # Display summary statistics at the top
    display_summary_statistics(filtered_data)

    # Create tabs for different analysis views
    create_analysis_tabs(filtered_data)


def display_summary_statistics(filtered_data):
    """Display key summary statistics in a row of metrics.

    Args:
        filtered_data: DataFrame containing the filtered data to analyze
    """
    stats = get_summary_stats(filtered_data)

    col1, col2, col3 = st.columns(3)
    with col1:
        st.metric("Gesamtanzahl Datensätze", stats['total_records'])
        st.metric("Anzahl LLMs", stats['unique_llms'])
    with col2:
        st.metric("Anzahl Kategorien", stats['unique_categories'])
        st.metric("Anzahl Testfälle", stats['unique_testcases'])
    with col3:
        st.metric("Durchschn. Antwortzeit (s)", round(stats['avg_response_time'], 2))
        st.metric("Durchschn. Characters/Sekunde", round(stats['avg_characters_per_second'], 2))


def create_analysis_tabs(filtered_data):
    """Create tabs for all categories vs. important categories analysis.

    Args:
        filtered_data: DataFrame containing the filtered data to analyze
    """

    tab1, tab2 = st.tabs(["Alle Kategorien", "Wichtige Kategorien"])

    # Define categories considered most important for analysis
    important_categories = [
        "SIMPLE_BOOKING",
        "MULTIPLE_BOOKINGS",
        "RELATIVE_TIME_BOOKING",
        "SEQUENTIAL_BOOKING",
        "COMBINED_COMPLEX_BOOKINGS"
    ]

    with tab1:
        display_all_charts(filtered_data, is_important_categories=False)

    with tab2:
        # Filter data to include only important categories
        important_data = filtered_data[filtered_data['category'].isin(important_categories)]
        display_all_charts(important_data, is_important_categories=True)


def display_all_charts(data, is_important_categories=False):
    """Display all visualization charts for the given data.

    Renders charts in a logical order: success rates, performance metrics,
    robustness analysis, error distribution, speed analysis, and response time estimation.

    Args:
        data: DataFrame containing the data to visualize
        is_important_categories: Whether this is the important categories view (affects thresholds)
    """
    # Primary success rate analysis
    st.plotly_chart(get_success_rate_for_llms_chart(data, is_important_categories=is_important_categories), use_container_width=True)

    # Combined performance score visualization
    st.plotly_chart(get_performance_score_chart(data), use_container_width=True)

    # Accuracy vs. Speed scatter plot analysis
    st.plotly_chart(get_accuracy_vs_speed_chart(data, is_important_categories=is_important_categories), use_container_width=True)

    # Category-specific success rate analysis
    if is_important_categories:
        # Show important categories with both 75% and 80% threshold lines
        st.plotly_chart(get_success_rate_for_important_categories(data), use_container_width=True)
    else:
        # Show all categories with standard 75% threshold line
        st.plotly_chart(get_success_rate_for_categories(data), use_container_width=True)

    # Robustness analysis across test cases
    robustness_chart, robustness_stats = get_variant_robustness_chart(data, is_important_categories=is_important_categories)
    st.plotly_chart(robustness_chart, use_container_width=True)
    st.subheader("Verteilung der Erfolgsraten nach LLM")
    st.dataframe(robustness_stats, use_container_width=True, hide_index=True)

    # Error type distribution analysis
    st.plotly_chart(get_error_distribution_chart(data), use_container_width=True)

    # Speed analysis for successful responses
    st.plotly_chart(get_characters_per_second_chart(data), use_container_width=True)

    # Response time estimation based on response length
    display_response_time_analysis(data)


def display_response_time_analysis(data):
    """Display response time estimation charts in organized tabs.

    Args:
        data: DataFrame containing the data for response time analysis
    """
    st.subheader("Geschätzte Antwortzeit nach Antwortlänge")
    response_time_tabs = st.tabs(["Säulendiagramm", "Liniendiagramm"])

    with response_time_tabs[0]:
        st.plotly_chart(get_response_time_estimation_chart(data), use_container_width=True)
    with response_time_tabs[1]:
        st.plotly_chart(get_response_time_line_chart(data), use_container_width=True)
