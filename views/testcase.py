"""Testcase view for detailed analysis of a specific testcase.

Provides focused analysis of LLM performance within a selected testcase,
including variant-level breakdowns and testcase-specific metrics.
"""

import streamlit as st

from components.visualizations.success_rate_for_variants import get_success_rate_for_variants_chart
from components.visualizations.summary_stats import get_summary_stats
from utils.state_management import get_state


def testcase(testcase_id):
    """Render detailed analysis page for a specific testcase.

    Displays testcase-specific performance metrics, variant breakdowns,
    and comparative visualizations for the selected testcase.

    Args:
        testcase_id: The ID of the testcase to analyze
    """
    # Get filtered data from session state
    raw_data = get_state('filtered_data')

    # Filter data to include only the selected testcase
    testcase_data = raw_data[raw_data['test_case_id'] == int(testcase_id)]

    st.title(f"Testfall: {testcase_id}")

    # Display testcase-specific summary statistics
    display_testcase_statistics(testcase_data)

    # Display testcase-specific visualizations
    display_testcase_charts(testcase_data)


def display_testcase_statistics(testcase_data):
    """Display summary statistics for the testcase.

    Args:
        testcase_data: DataFrame containing data filtered for the specific testcase
    """
    stats = get_summary_stats(testcase_data)

    col1, col2, col3, col4 = st.columns(4)
    with col1:
        st.metric("Anzahl Varianten", len(testcase_data['variant_number'].unique()))
    with col2:
        st.metric("Gesamtanzahl Datensätze", stats['total_records'])
    with col3:
        st.metric("Durchschn. Antwortzeit (s)", round(stats['avg_response_time'], 2))
    with col4:
        st.metric("Durchschn. Characters/Sekunde", round(stats['avg_characters_per_second'], 2))


def display_testcase_charts(testcase_data):
    """Display testcase-specific visualization charts.

    Args:
        testcase_data: DataFrame containing data filtered for the specific testcase
    """
    # Success rate breakdown by variants within this testcase
    st.plotly_chart(get_success_rate_for_variants_chart(testcase_data), use_container_width=True)
