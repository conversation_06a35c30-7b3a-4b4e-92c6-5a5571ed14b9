"""Variant view for detailed analysis of a specific testcase variant.

Provides the most detailed level of analysis, showing individual LLM responses,
performance metrics, and detailed breakdowns for a specific variant.
"""

import streamlit as st

from components.visualizations.success_rate_for_variants import get_success_rate_for_variants_chart
from components.visualizations.summary_stats import get_summary_stats
from utils.data_functions import get_llms
from utils.state_management import get_state


def variant(testcase_id, variant_number):
    """Render detailed analysis page for a specific testcase variant.

    Displays variant-specific performance metrics, LLM comparison charts,
    detailed response data, and individual LLM response analysis.

    Args:
        testcase_id: The ID of the testcase
        variant_number: The variant number to analyze
    """
    st.title(f"Variante {variant_number} für Testfall {testcase_id}")

    # Get filtered data from session state
    raw_data = get_state('filtered_data')

    # Filter data to include only the selected testcase and variant
    variant_data = raw_data[
        (raw_data['test_case_id'] == int(testcase_id)) &
        (raw_data['variant_number'] == int(variant_number))
    ]

    # Display variant-specific summary statistics
    display_variant_statistics(variant_data)

    # Display variant-specific visualizations
    display_variant_charts(variant_data)

    # Display detailed response data table
    display_response_data_table(variant_data)

    # Display detailed LLM response analysis
    display_llm_response_details(variant_data, raw_data)


def display_variant_statistics(variant_data):
    """Display summary statistics for the variant.

    Args:
        variant_data: DataFrame containing data filtered for the specific variant
    """
    stats = get_summary_stats(variant_data)

    col1, col2, col3 = st.columns(3)
    with col1:
        st.metric("Gesamtanzahl Datensätze", stats['total_records'])
    with col2:
        st.metric("Durchschn. Antwortzeit (s)", round(stats['avg_response_time'], 2))
    with col3:
        st.metric("Durchschn. Characters/Sekunde", round(stats['avg_characters_per_second'], 2))


def display_variant_charts(variant_data):
    """Display variant-specific visualization charts.

    Args:
        variant_data: DataFrame containing data filtered for the specific variant
    """
    # Success rate comparison across LLMs for this variant
    st.plotly_chart(get_success_rate_for_variants_chart(variant_data), use_container_width=True)


def display_response_data_table(variant_data):
    """Display a formatted table with response data for all LLMs.

    Args:
        variant_data: DataFrame containing data filtered for the specific variant
    """

    # Create formatted table with response data
    response_table = variant_data[['llm', 'repetition_number', 'successful', 'error', 'response_time', 'characters_per_second']].copy()

    # Rename columns for better display
    response_table.columns = ['LLM', 'Wiederholung', 'Status', 'Fehlertyp', 'Antwortzeit (s)', 'Characters/Sekunde']

    # Format numeric columns with consistent decimal places
    response_table['Antwortzeit (s)'] = response_table['Antwortzeit (s)'].apply(lambda x: f'{x:.2f}')
    response_table['Characters/Sekunde'] = response_table['Characters/Sekunde'].apply(lambda x: f'{x:.2f}')

    # Use visual indicators for success status
    response_table['Status'] = response_table['Status'].map({1: '✅', 0: '❌'})

    # Clean up error display
    response_table['Fehlertyp'] = response_table['Fehlertyp'].fillna('-')

    st.dataframe(
        response_table,
        use_container_width=True,
        hide_index=True
    )


def display_llm_response_details(variant_data, raw_data):
    """Display detailed response analysis for each LLM with expandable sections.

    Args:
        variant_data: DataFrame containing data filtered for the specific variant
        raw_data: Full dataset for getting LLM order
    """

    # Get all LLMs in original database order
    available_llms = get_llms(raw_data)

    # Display detailed analysis for each LLM
    for llm_name in available_llms:
        llm_data = variant_data[variant_data['llm'] == llm_name]

        if len(llm_data) > 0:
            # Calculate success rate for this LLM
            success_count = llm_data['successful'].sum()
            total_count = len(llm_data)

            # Create expandable section with success rate in title
            with st.expander(f"{llm_name} ({success_count}/{total_count})"):
                display_llm_repetition_details(llm_data)


def display_llm_repetition_details(llm_data):
    """Display detailed analysis for all repetitions of a specific LLM.

    Args:
        llm_data: DataFrame containing data for a specific LLM
    """
    # Get all repetitions for this LLM and sort them
    repetition_numbers = sorted(llm_data["repetition_number"].unique())
    repetition_tabs = st.tabs([f"Wiederholung {rep}" for rep in repetition_numbers])

    for tab, repetition_number in zip(repetition_tabs, repetition_numbers):
        with tab:
            # Get data for this specific repetition
            repetition_result = llm_data[
                llm_data['repetition_number'] == repetition_number
            ].iloc[0]

            # Display performance metrics for this repetition
            display_repetition_metrics(repetition_result)

            # Display the actual response
            display_response_content(repetition_result)


def display_repetition_metrics(repetition_result):
    """Display performance metrics for a single repetition.

    Args:
        repetition_result: Series containing data for a single repetition
    """
    col1, col2, col3 = st.columns(3)
    with col1:
        status_text = "Erfolgreich" if repetition_result['successful'] else "Fehlgeschlagen"
        st.metric("Status", status_text)
    with col2:
        st.metric("Antwortzeit (s)", round(repetition_result['response_time'], 2))
    with col3:
        st.metric("Characters/Sekunde", round(repetition_result['characters_per_second'], 2))

    # Show error information if the response was not successful
    if not repetition_result["successful"]:
        st.error(f"Fehlertyp: {repetition_result['error']}")


def display_response_content(repetition_result):
    """Display the actual response content.

    Args:
        repetition_result: Series containing data for a single repetition
    """
    st.subheader("Antwort")
    st.code(repetition_result['response'])
